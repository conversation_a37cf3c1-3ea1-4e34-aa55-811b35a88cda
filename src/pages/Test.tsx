import supabase from "@/utils/supabase.ts";
import { useState } from "react";

interface ContactPharmacie {
  //   id: number;
  numero: string;
  id_pharmacie: number;
}

interface Pharmacie {
  id: number;
  nom_pharmacie: string;
  address: string;
  province: string;
  service: string;
  photo_profil?: string;
  contact_pharmacies: ContactPharmacie[];
}

const Test = () => {
  const [data, setData] = useState<Pharmacie[] | null>(null);

  const getData = async () => {
    const response = await supabase.from("pharmacies").select(
      `id,
        nom_pharmacie,
        address,
        province,
        service,
        photo_profil,
        contact_pharmacies(
          numero,
          id_pharmacie
        )
      `
    );
    setData(response.data);
  };

  const saveToJson = () => {
    if (!data) return;

    // Créer un objet Blob avec les données
    const jsonString = JSON.stringify(data, null, 2);
    const blob = new Blob([jsonString], { type: "application/json" });

    // Créer une URL pour le Blob
    const url = URL.createObjectURL(blob);

    // Créer un élément <a> pour le téléchargement
    const link = document.createElement("a");
    link.href = url;

    // Générer un nom de fichier avec la date actuelle
    const date = new Date().toISOString().split("T")[0];
    link.download = `pharmacies_${date}.json`;

    // Ajouter le lien au document, cliquer dessus, puis le supprimer
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Libérer l'URL
    URL.revokeObjectURL(url);
  };

  return (
    <div className="p-4 space-y-4">
      <div className="space-x-4">
        <button
          onClick={getData}
          className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
        >
          Récupérer les données
        </button>
        <button
          onClick={saveToJson}
          className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded"
          disabled={!data}
        >
          Sauvegarder en JSON
        </button>
      </div>

      {data && (
        <div className="mt-4">
          <h2 className="text-lg font-semibold mb-2">Aperçu des données :</h2>
          <pre className="bg-gray-100 p-4 rounded overflow-auto max-h-[500px]">
            {JSON.stringify(data, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
};

export default Test;
