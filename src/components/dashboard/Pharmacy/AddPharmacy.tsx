import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useEffect, useRef, useState } from "react";
import { z } from "zod";
import PharmacyForm from "./PharmacyForm";

// Schéma de validation Zod
const pharmacySchema = z.object({
  nom_pharmacie: z.string().min(1, "Le nom est requis"),
  address: z.string().min(1, "L'adresse est requise"),
  province: z.string().min(1, "La province est requise"),
  service: z.string().min(1, "Au moins un service est requis"),
  contacts: z
    .array(
      z.object({
        numero: z
          .string()
          .regex(/^[0-9+\s-]{8,}$/, "Numéro de téléphone invalide"),
      })
    )
    .min(1, "Au moins un contact est requis"),
});

type NewPharmacyData = Omit<Pharmacy, "id">;

interface AddPharmacyProps {
  onSubmit: (data: Pharmacy, file?: File) => void;
  pharmacy?: Pharmacy;
  isEdit?: boolean;
  isAddDialogOpen: boolean;
  setIsAddDialogOpen: (value: boolean) => void;
}

export default function AddPharmacy({
  onSubmit,
  pharmacy,
  isEdit = false,
  isAddDialogOpen,
  setIsAddDialogOpen,
}: AddPharmacyProps) {
  const [formData, setFormData] = useState<
    Omit<Pharmacy, "id"> & { id?: number }
  >({
    nom_pharmacie: pharmacy?.nom_pharmacie || "",
    photo_profil: pharmacy?.photo_profil || "",
    address: pharmacy?.address || "",
    province: pharmacy?.province || "",
    service: pharmacy?.service || "",
    contacts: pharmacy?.contacts || [{ numero: "" }],
    ...(isEdit && pharmacy ? { id: pharmacy.id } : {}),
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (pharmacy) {
      setFormData({
        ...pharmacy,
        contacts: pharmacy.contacts || [{ numero: "" }],
      });
    }
  }, [pharmacy]);

  const handleServiceChange = (val: string) => {
    setFormData((prev) => ({
      ...prev,
      service: val,
    }));
    if (val) {
      setErrors((prev) => ({ ...prev, service: "" }));
    }
  };

  const validateFormData = () => {
    try {
      pharmacySchema.parse(formData);
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors: Record<string, string> = {};
        error.errors.forEach((err) => {
          const path = err.path.join(".");
          newErrors[path] = err.message;
        });
        setErrors(newErrors);
      }
      return false;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (validateFormData()) {
      try {
        // Pour une nouvelle pharmacie, on envoie les données sans l'ID
        const submissionData = isEdit
          ? formData
          : {
              nom_pharmacie: formData.nom_pharmacie,
              photo_profil: formData.photo_profil,
              address: formData.address,
              province: formData.province,
              service: formData.service,
              contacts: formData.contacts,
            };

        onSubmit(submissionData as Pharmacy, selectedFile || undefined);
        setIsAddDialogOpen(false);
      } catch (error) {
        console.error("Erreur lors de la soumission:", error);
      }
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setSelectedFile(file);
      setFormData((prev) => ({
        ...prev,
        photo_profil: URL.createObjectURL(file),
      }));
    }
  };

  const handleOpenChange = (open: boolean) => {
    setIsAddDialogOpen(open);
    if (!open) {
      setFormData({
        nom_pharmacie: "",
        photo_profil: "",
        address: "",
        province: "",
        service: "",
        contacts: [{ numero: "" }],
      });
      setErrors({});
      setSelectedFile(null);
    }
  };

  return (
    <Dialog open={isAddDialogOpen} onOpenChange={handleOpenChange}>
      {!isEdit && (
        <DialogTrigger asChild>
          <Button className="w-full md:w-auto">Ajouter une pharmacie</Button>
        </DialogTrigger>
      )}
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEdit
              ? "Modifier la pharmacie"
              : "Ajouter une nouvelle pharmacie"}
          </DialogTitle>
        </DialogHeader>
        <PharmacyForm
          handleSubmit={handleSubmit}
          formData={formData as Pharmacy}
          setFormData={setFormData}
          errors={errors}
          fileInputRef={fileInputRef}
          handleFileChange={handleFileChange}
          handleServiceChange={handleServiceChange}
        />
      </DialogContent>
    </Dialog>
  );
}
