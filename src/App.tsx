import PrivateRoute from "@/components/PrivateRoute";
import PublicLayout from "@/components/PublicLayout";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { useEffect } from "react";
import { Provider } from "react-redux";
import { BrowserRouter, Route, Routes, useLocation } from "react-router-dom";
import { Toaster } from "sonner";
import ContactsUs from "./components/dashboard/contactUs/ContactsUs";
import DashboardLayout from "./components/dashboard/DashboardLayout";
import { ServiceProvider } from "./contexts/ServiceContext";
import About from "./pages/About";
import AppMeddoc from "./pages/AppMeddoc";
import ContactUs from "./pages/ContactUs";
import AmbulanceList from "./pages/Dashboard/Ambulance/AmbulanceList";
import EditPageIndex from "./pages/Dashboard/EditPages/EditPageIndex";
import DashboardIndex from "./pages/Dashboard/Index";
import LibraryDashboard from "./pages/Dashboard/Library/LibraryDashboard";
import NumberList from "./pages/Dashboard/Numbers/NumberList";
import PartnerIndex from "./pages/Dashboard/Partners/PartnerIndex";
import PartnerList from "./pages/Dashboard/Partners/PartnerList";
import DashboardPharmacies from "./pages/Dashboard/Pharmacy/PharmacyList";
import Services from "./pages/Dashboard/Services";
import SocialMediaIndex from "./pages/Dashboard/SocialMedia/SocialMediaIndex";
import DigitalLibrary from "./pages/DigitalLibrary";
import Index from "./pages/Index";
import Login from "./pages/Login";
import Pharmacy from "./pages/Pharmacy";
import Community from "./pages/services/Community";
import Consulting from "./pages/services/Consulting";
import Digital from "./pages/services/Digital";
import Formations from "./pages/services/Formations";
import Test from "./pages/Test";
import { store } from "./store";

const queryClient = new QueryClient();

// Composant pour gérer le défilement vers les ancres
const ScrollToAnchor = () => {
  const { hash, pathname } = useLocation();

  useEffect(() => {
    // Si un hash est présent dans l'URL
    if (hash) {
      // Attendre que le DOM soit complètement chargé
      setTimeout(() => {
        const id = hash.replace("#", "");
        const element = document.getElementById(id);
        if (element) {
          const offset = 80; // Offset pour tenir compte du header fixe
          const elementPosition =
            element.getBoundingClientRect().top + window.scrollY;
          window.scrollTo({
            top: elementPosition - offset,
            behavior: "smooth",
          });

          // Ajouter un effet de surbrillance
          element.classList.add("highlight-section");
          setTimeout(() => {
            element.classList.remove("highlight-section");
          }, 1500);
        }
      }, 100);
    } else {
      // Défiler vers le haut lors d'un changement de page sans hash
      window.scrollTo(0, 0);
    }
  }, [pathname, hash]); // Réagir aux changements de pathname et hash

  return null;
};

// Wrapper pour utiliser le hook useLocation
const AppContent = () => {
  return (
    <>
      <ScrollToAnchor />
      <Routes>
        {/* Routes publiques */}
        <Route
          path="/"
          element={
            <PublicLayout>
              <Index />
            </PublicLayout>
          }
        />
        <Route
          path="/login"
          element={
            <PublicLayout>
              <Login />
            </PublicLayout>
          }
        />
        <Route
          path="/app-meddoc"
          element={
            <PublicLayout>
              <AppMeddoc />
            </PublicLayout>
          }
        />
        <Route
          path="/pharmacies"
          element={
            <PublicLayout>
              <Pharmacy />
            </PublicLayout>
          }
        />
        <Route
          path="/contact"
          element={
            <PublicLayout>
              <ContactUs />
            </PublicLayout>
          }
        />
        <Route
          path="/apropos"
          element={
            <PublicLayout>
              <About />
            </PublicLayout>
          }
        />
        <Route
          path="/bibliotheque"
          element={
            <PublicLayout>
              <DigitalLibrary />
            </PublicLayout>
          }
        />
        {/* Routes des services */}
        <Route
          path="/services/community"
          element={
            <PublicLayout>
              <Community />
            </PublicLayout>
          }
        />
        <Route
          path="/services/consulting"
          element={
            <PublicLayout>
              <Consulting />
            </PublicLayout>
          }
        />
        <Route
          path="/services/digital"
          element={
            <PublicLayout>
              <Digital />
            </PublicLayout>
          }
        />
        <Route
          path="/services/formations"
          element={
            <PublicLayout>
              <Formations />
            </PublicLayout>
          }
        />
        <Route path="/test" element={<Test />} />
        {/* Routes protégées pour le tableau de bord */}
        <Route element={<PrivateRoute />}>
          <Route element={<DashboardLayout />}>
            <Route path="/dashboard" element={<DashboardIndex />} />
            <Route
              path="/dashboard/pharmacies"
              element={<DashboardPharmacies />}
            />
            {/* Edition de page */}
            <Route path="/dashboard/page-meddoc/" element={<EditPageIndex />} />
            <Route path="/dashboard/partenaires" element={<PartnerIndex />}>
              <Route
                path="/dashboard/partenaires/list"
                element={<PartnerList />}
              />
            </Route>
            <Route
              path="/dashboard/reseaux-sociaux"
              element={<SocialMediaIndex />}
            />
            <Route path="/dashboard/services" element={<Services />} />
            <Route path="/dashboard/contact-meddoc" element={<ContactsUs />} />
            <Route path="/dashboard/contacts" element={<NumberList />} />
            <Route path="/dashboard/ambulances" element={<AmbulanceList />} />
            <Route
              path="/dashboard/bibliotheque"
              element={<LibraryDashboard />}
            />
          </Route>
        </Route>
      </Routes>
    </>
  );
};

// Composant principal de l'application
const App = () => {
  return (
    <Provider store={store}>
      <QueryClientProvider client={queryClient}>
        <Provider store={store}>
          <TooltipProvider>
            <ServiceProvider>
              <Toaster richColors />
              <BrowserRouter>
                <AppContent />
              </BrowserRouter>
            </ServiceProvider>
          </TooltipProvider>
        </Provider>
      </QueryClientProvider>
    </Provider>
  );
};

export default App;
